{"name": "yargs", "version": "13.3.2", "description": "yargs the modern, pirate-themed, successor to optimist.", "main": "./index.js", "contributors": [{"name": "Yargs Contributors", "url": "https://github.com/yargs/yargs/graphs/contributors"}], "files": ["index.js", "yargs.js", "lib", "locales", "completion.sh.hbs", "completion.zsh.hbs", "LICENSE"], "dependencies": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.2"}, "devDependencies": {"chai": "^4.2.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "cpr": "^3.0.1", "cross-spawn": "^6.0.4", "es6-promise": "^4.2.5", "hashish": "0.0.4", "mocha": "^5.2.0", "nyc": "^14.1.0", "rimraf": "^2.6.3", "standard": "^12.0.1", "standard-version": "^6.0.1", "which": "^1.3.1", "yargs-test-extends": "^1.0.1"}, "scripts": {"pretest": "standard", "test": "nyc --cache mocha --require ./test/before.js --timeout=12000 --check-leaks", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "https://github.com/yargs/yargs.git"}, "homepage": "https://yargs.js.org/", "standard": {"ignore": ["**/example/**"]}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "engine": {"node": ">=6"}}