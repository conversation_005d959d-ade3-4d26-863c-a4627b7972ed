{"name": "vitest-environment-clarinet", "version": "2.3.0", "description": "A vitest environment to test Clarinet projects", "main": "index.js", "type": "module", "repository": {"type": "git", "url": "https://github.com/hirosystems/vitest-environment-clarinet"}, "scripts": {}, "keywords": ["clarinet", "clarity", "test", "stacks", "vitest"], "author": "hirosystems", "license": "GPL-3.0", "peerDependencies": {"@hirosystems/clarinet-sdk": ">=2.14.0", "vitest": "^1.0.0 || ^2.0.0 || ^3.0.0"}}