import { M as ModuleMockerInterceptor, a as ModuleMockerCompilerHints, b as ModuleMocker } from './mocker.d-Ce9_ySj5.js';
import '@vitest/spy';
import './types.d-D_aRZRdy.js';
import './registry.d-D765pazg.js';

declare function registerModuleMocker(interceptor: (accessor: string) => ModuleMockerInterceptor): ModuleMockerCompilerHints;
declare function registerNativeFactoryResolver(mocker: ModuleMocker): void;

export { registerModuleMocker, registerNativeFactoryResolver };
