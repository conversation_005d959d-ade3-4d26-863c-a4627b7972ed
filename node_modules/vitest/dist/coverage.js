export { B as BaseCoverageProvider } from './chunks/coverage.DL5VHqXY.js';
import 'node:fs';
import 'pathe';
import 'picomatch';
import 'tinyrainbow';
import './chunks/defaults.B7q_naMc.js';
import 'node:os';
import './chunks/env.D4Lgay0q.js';
import 'std-env';
import 'node:crypto';
import '@vitest/utils';
import 'node:module';
import 'node:path';
import 'node:process';
import 'node:fs/promises';
import 'node:url';
import 'node:assert';
import 'node:v8';
import 'node:util';
import 'vite';
import './chunks/constants.DnKduX2e.js';
import 'node:tty';
import 'node:events';
import './chunks/index.B521nVV-.js';
import 'tinypool';
import './chunks/typechecker.DRKU1-1g.js';
import 'node:perf_hooks';
import '@vitest/utils/source-map';
import 'tinyexec';
import './path.js';
import '@vitest/runner/utils';
import 'node:worker_threads';
import 'vite-node/utils';
import './chunks/coverage.DVF1vEu8.js';
