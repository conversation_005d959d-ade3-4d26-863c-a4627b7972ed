/*! For license information please see c32check.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.c32check=t():e.c32check=t()}(this,(()=>(()=>{"use strict";var e={320:(e,t)=>{function r(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`Wrong positive integer: ${e}`)}function n(e){if("boolean"!=typeof e)throw new Error(`Expected boolean, not ${e}`)}function o(e,...t){if(!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(t.length>0&&!t.includes(e.length))throw new TypeError(`Expected Uint8Array of length ${t}, not of length=${e.length}`)}function s(e){if("function"!=typeof e||"function"!=typeof e.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");r(e.outputLen),r(e.blockLen)}function i(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function c(e,t){o(e);const r=t.outputLen;if(e.length<r)throw new Error(`digestInto() expects output buffer of length at least ${r}`)}Object.defineProperty(t,"__esModule",{value:!0}),t.output=t.exists=t.hash=t.bytes=t.bool=t.number=void 0,t.number=r,t.bool=n,t.bytes=o,t.hash=s,t.exists=i,t.output=c;const f={number:r,bool:n,bytes:o,hash:s,exists:i,output:c};t.default=f},505:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SHA2=void 0;const n=r(320),o=r(89);class s extends o.Hash{constructor(e,t,r,n){super(),this.blockLen=e,this.outputLen=t,this.padOffset=r,this.isLE=n,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=(0,o.createView)(this.buffer)}update(e){n.default.exists(this);const{view:t,buffer:r,blockLen:s}=this,i=(e=(0,o.toBytes)(e)).length;for(let n=0;n<i;){const c=Math.min(s-this.pos,i-n);if(c!==s)r.set(e.subarray(n,n+c),this.pos),this.pos+=c,n+=c,this.pos===s&&(this.process(t,0),this.pos=0);else{const t=(0,o.createView)(e);for(;s<=i-n;n+=s)this.process(t,n)}}return this.length+=e.length,this.roundClean(),this}digestInto(e){n.default.exists(this),n.default.output(e,this),this.finished=!0;const{buffer:t,view:r,blockLen:s,isLE:i}=this;let{pos:c}=this;t[c++]=128,this.buffer.subarray(c).fill(0),this.padOffset>s-c&&(this.process(r,0),c=0);for(let e=c;e<s;e++)t[e]=0;!function(e,t,r,n){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,r,n);const o=BigInt(32),s=BigInt(4294967295),i=Number(r>>o&s),c=Number(r&s),f=n?4:0,a=n?0:4;e.setUint32(t+f,i,n),e.setUint32(t+a,c,n)}(r,s-8,BigInt(8*this.length),i),this.process(r,0);const f=(0,o.createView)(e);this.get().forEach(((e,t)=>f.setUint32(4*t,e,i)))}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const r=e.slice(0,t);return this.destroy(),r}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:t,buffer:r,length:n,finished:o,destroyed:s,pos:i}=this;return e.length=n,e.pos=i,e.finished=o,e.destroyed=s,n%t&&e.buffer.set(r),e}}t.SHA2=s},421:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0,t.crypto={node:void 0,web:"object"==typeof self&&"crypto"in self?self.crypto:void 0}},61:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.sha256=void 0;const n=r(505),o=r(89),s=(e,t,r)=>e&t^e&r^t&r,i=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),c=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),f=new Uint32Array(64);class a extends n.SHA2{constructor(){super(64,32,8,!1),this.A=0|c[0],this.B=0|c[1],this.C=0|c[2],this.D=0|c[3],this.E=0|c[4],this.F=0|c[5],this.G=0|c[6],this.H=0|c[7]}get(){const{A:e,B:t,C:r,D:n,E:o,F:s,G:i,H:c}=this;return[e,t,r,n,o,s,i,c]}set(e,t,r,n,o,s,i,c){this.A=0|e,this.B=0|t,this.C=0|r,this.D=0|n,this.E=0|o,this.F=0|s,this.G=0|i,this.H=0|c}process(e,t){for(let r=0;r<16;r++,t+=4)f[r]=e.getUint32(t,!1);for(let e=16;e<64;e++){const t=f[e-15],r=f[e-2],n=(0,o.rotr)(t,7)^(0,o.rotr)(t,18)^t>>>3,s=(0,o.rotr)(r,17)^(0,o.rotr)(r,19)^r>>>10;f[e]=s+f[e-7]+n+f[e-16]|0}let{A:r,B:n,C:c,D:a,E:h,F:u,G:d,H:l}=this;for(let e=0;e<64;e++){const t=l+((0,o.rotr)(h,6)^(0,o.rotr)(h,11)^(0,o.rotr)(h,25))+((p=h)&u^~p&d)+i[e]+f[e]|0,y=((0,o.rotr)(r,2)^(0,o.rotr)(r,13)^(0,o.rotr)(r,22))+s(r,n,c)|0;l=d,d=u,u=h,h=a+t|0,a=c,c=n,n=r,r=t+y|0}var p;r=r+this.A|0,n=n+this.B|0,c=c+this.C|0,a=a+this.D|0,h=h+this.E|0,u=u+this.F|0,d=d+this.G|0,l=l+this.H|0,this.set(r,n,c,a,h,u,d,l)}roundClean(){f.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}t.sha256=(0,o.wrapConstructor)((()=>new a))},89:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.randomBytes=t.wrapConstructorWithOpts=t.wrapConstructor=t.checkOpts=t.Hash=t.concatBytes=t.toBytes=t.utf8ToBytes=t.asyncLoop=t.nextTick=t.hexToBytes=t.bytesToHex=t.isLE=t.rotr=t.createView=t.u32=t.u8=void 0;const n=r(421);if(t.u8=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),t.u32=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4)),t.createView=e=>new DataView(e.buffer,e.byteOffset,e.byteLength),t.rotr=(e,t)=>e<<32-t|e>>>t,t.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0],!t.isLE)throw new Error("Non little-endian hardware is not supported");const o=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function s(e){if("string"!=typeof e)throw new TypeError("utf8ToBytes expected string, got "+typeof e);return(new TextEncoder).encode(e)}function i(e){if("string"==typeof e&&(e=s(e)),!(e instanceof Uint8Array))throw new TypeError(`Expected input type is Uint8Array (got ${typeof e})`);return e}t.bytesToHex=function(e){if(!(e instanceof Uint8Array))throw new Error("Uint8Array expected");let t="";for(let r=0;r<e.length;r++)t+=o[e[r]];return t},t.hexToBytes=function(e){if("string"!=typeof e)throw new TypeError("hexToBytes: expected string, got "+typeof e);if(e.length%2)throw new Error("hexToBytes: received invalid unpadded hex");const t=new Uint8Array(e.length/2);for(let r=0;r<t.length;r++){const n=2*r,o=e.slice(n,n+2),s=Number.parseInt(o,16);if(Number.isNaN(s)||s<0)throw new Error("Invalid byte sequence");t[r]=s}return t},t.nextTick=async()=>{},t.asyncLoop=async function(e,r,n){let o=Date.now();for(let s=0;s<e;s++){n(s);const e=Date.now()-o;e>=0&&e<r||(await(0,t.nextTick)(),o+=e)}},t.utf8ToBytes=s,t.toBytes=i,t.concatBytes=function(...e){if(!e.every((e=>e instanceof Uint8Array)))throw new Error("Uint8Array list expected");if(1===e.length)return e[0];const t=e.reduce(((e,t)=>e+t.length),0),r=new Uint8Array(t);for(let t=0,n=0;t<e.length;t++){const o=e[t];r.set(o,n),n+=o.length}return r},t.Hash=class{clone(){return this._cloneInto()}},t.checkOpts=function(e,t){if(void 0!==t&&("object"!=typeof t||(r=t,"[object Object]"!==Object.prototype.toString.call(r)||r.constructor!==Object)))throw new TypeError("Options should be object or undefined");var r;return Object.assign(e,t)},t.wrapConstructor=function(e){const t=t=>e().update(i(t)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t},t.wrapConstructorWithOpts=function(e){const t=(t,r)=>e(r).update(i(t)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=t=>e(t),t},t.randomBytes=function(e=32){if(n.crypto.web)return n.crypto.web.getRandomValues(new Uint8Array(e));if(n.crypto.node)return new Uint8Array(n.crypto.node.randomBytes(e).buffer);throw new Error("The environment doesn't have randomBytes function")}},162:e=>{e.exports=function(e){if(e.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),r=0;r<t.length;r++)t[r]=255;for(var n=0;n<e.length;n++){var o=e.charAt(n),s=o.charCodeAt(0);if(255!==t[s])throw new TypeError(o+" is ambiguous");t[s]=n}var i=e.length,c=e.charAt(0),f=Math.log(i)/Math.log(256),a=Math.log(256)/Math.log(i);function h(e){if("string"!=typeof e)throw new TypeError("Expected String");if(0===e.length)return new Uint8Array;for(var r=0,n=0,o=0;e[r]===c;)n++,r++;for(var s=(e.length-r)*f+1>>>0,a=new Uint8Array(s);e[r];){var h=t[e.charCodeAt(r)];if(255===h)return;for(var u=0,d=s-1;(0!==h||u<o)&&-1!==d;d--,u++)h+=i*a[d]>>>0,a[d]=h%256>>>0,h=h/256>>>0;if(0!==h)throw new Error("Non-zero carry");o=u,r++}for(var l=s-o;l!==s&&0===a[l];)l++;for(var p=new Uint8Array(n+(s-l)),y=n;l!==s;)p[y++]=a[l++];return p}return{encode:function(t){if(t instanceof Uint8Array||(ArrayBuffer.isView(t)?t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength):Array.isArray(t)&&(t=Uint8Array.from(t))),!(t instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===t.length)return"";for(var r=0,n=0,o=0,s=t.length;o!==s&&0===t[o];)o++,r++;for(var f=(s-o)*a+1>>>0,h=new Uint8Array(f);o!==s;){for(var u=t[o],d=0,l=f-1;(0!==u||d<n)&&-1!==l;l--,d++)u+=256*h[l]>>>0,h[l]=u%i>>>0,u=u/i>>>0;if(0!==u)throw new Error("Non-zero carry");n=d,o++}for(var p=f-n;p!==f&&0===h[p];)p++;for(var y=c.repeat(r);p<f;++p)y+=e.charAt(h[p]);return y},decodeUnsafe:h,decode:function(e){var t=h(e);if(t)return t;throw new Error("Non-base"+i+" character")}}}},959:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.c32ToB58=t.b58ToC32=t.c32addressDecode=t.c32address=t.versions=void 0;const n=r(997),o=r(97),s=r(89);t.versions={mainnet:{p2pkh:22,p2sh:20},testnet:{p2pkh:26,p2sh:21}};const i={};i[0]=t.versions.mainnet.p2pkh,i[5]=t.versions.mainnet.p2sh,i[111]=t.versions.testnet.p2pkh,i[196]=t.versions.testnet.p2sh;const c={};function f(e,t){if(!t.match(/^[0-9a-fA-F]{40}$/))throw new Error("Invalid argument: not a hash160 hex string");return`S${(0,n.c32checkEncode)(e,t)}`}function a(e){if(e.length<=5)throw new Error("Invalid c32 address: invalid length");if("S"!=e[0])throw new Error('Invalid c32 address: must start with "S"');return(0,n.c32checkDecode)(e.slice(1))}c[t.versions.mainnet.p2pkh]=0,c[t.versions.mainnet.p2sh]=5,c[t.versions.testnet.p2pkh]=111,c[t.versions.testnet.p2sh]=196,t.c32address=f,t.c32addressDecode=a,t.b58ToC32=function(e,t=-1){const r=o.decode(e),n=(0,s.bytesToHex)(r.data),c=parseInt((0,s.bytesToHex)(r.prefix),16);let a;return t<0?(a=c,void 0!==i[c]&&(a=i[c])):a=t,f(a,n)},t.c32ToB58=function(e,t=-1){const r=a(e),n=r[0],s=r[1];let i;t<0?(i=n,void 0!==c[n]&&(i=c[n])):i=t;let f=i.toString(16);return 1===f.length&&(f=`0${f}`),o.encode(s,f)}},97:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.decode=t.encode=void 0;const n=r(61),o=r(89),s=r(162),i="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";t.encode=function(e,t="00"){const r="string"==typeof e?(0,o.hexToBytes)(e):e,c="string"==typeof t?(0,o.hexToBytes)(t):e;if(!(r instanceof Uint8Array&&c instanceof Uint8Array))throw new TypeError("Argument must be of type Uint8Array or string");const f=(0,n.sha256)((0,n.sha256)(new Uint8Array([...c,...r])));return s(i).encode([...c,...r,...f.slice(0,4)])},t.decode=function(e){const t=s(i).decode(e),r=t.slice(0,1),o=t.slice(1,-4),c=(0,n.sha256)((0,n.sha256)(new Uint8Array([...r,...o])));return t.slice(-4).forEach(((e,t)=>{if(e!==c[t])throw new Error("Invalid checksum")})),{prefix:r,data:o}}},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.c32checkDecode=t.c32checkEncode=void 0;const n=r(61),o=r(89),s=r(558);function i(e){const t=(0,n.sha256)((0,n.sha256)((0,o.hexToBytes)(e)));return(0,o.bytesToHex)(t.slice(0,4))}t.c32checkEncode=function(e,t){if(e<0||e>=32)throw new Error("Invalid version (must be between 0 and 31)");if(!t.match(/^[0-9a-fA-F]*$/))throw new Error("Invalid data (not a hex string)");(t=t.toLowerCase()).length%2!=0&&(t=`0${t}`);let r=e.toString(16);1===r.length&&(r=`0${r}`);const n=i(`${r}${t}`),o=(0,s.c32encode)(`${t}${n}`);return`${s.c32[e]}${o}`},t.c32checkDecode=function(e){e=(0,s.c32normalize)(e);const t=(0,s.c32decode)(e.slice(1)),r=e[0],n=s.c32.indexOf(r),o=t.slice(-8);let c=n.toString(16);if(1===c.length&&(c=`0${c}`),i(`${c}${t.substring(0,t.length-8)}`)!==o)throw new Error("Invalid c32check string: checksum mismatch");return[n,t.substring(0,t.length-8)]}},558:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.c32decode=t.c32normalize=t.c32encode=t.c32=void 0;const n=r(89);t.c32="0123456789ABCDEFGHJKMNPQRSTVWXYZ";const o="0123456789abcdef";function s(e){return e.toUpperCase().replace(/O/g,"0").replace(/L|I/g,"1")}t.c32encode=function(e,r){if(!e.match(/^[0-9a-fA-F]*$/))throw new Error("Not a hex-encoded string");e.length%2!=0&&(e=`0${e}`);let s=[],i=0;for(let r=(e=e.toLowerCase()).length-1;r>=0;r--)if(i<4){const n=o.indexOf(e[r])>>i;let c=0;0!==r&&(c=o.indexOf(e[r-1]));const f=1+i,a=c%(1<<f)<<5-f,h=t.c32[n+a];i=f,s.unshift(h)}else i=0;let c=0;for(let e=0;e<s.length&&"0"===s[e];e++)c++;s=s.slice(c);const f=(new TextDecoder).decode((0,n.hexToBytes)(e)).match(/^\u0000*/),a=f?f[0].length:0;for(let e=0;e<a;e++)s.unshift(t.c32[0]);if(r){const e=r-s.length;for(let r=0;r<e;r++)s.unshift(t.c32[0])}return s.join("")},t.c32normalize=s,t.c32decode=function(e,r){if(!(e=s(e)).match(`^[${t.c32}]*$`))throw new Error("Not a c32-encoded string");const n=e.match(`^${t.c32[0]}*`),i=n?n[0].length:0;let c=[],f=0,a=0;for(let r=e.length-1;r>=0;r--){4===a&&(c.unshift(o[f]),a=0,f=0);const n=(t.c32.indexOf(e[r])<<a)+f,s=o[n%16];if(a+=1,f=n>>4,f>1<<a)throw new Error("Panic error in decoding.");c.unshift(s)}c.unshift(o[f]),c.length%2==1&&c.unshift("0");let h=0;for(let e=0;e<c.length&&"0"===c[e];e++)h++;c=c.slice(h-h%2);let u=c.join("");for(let e=0;e<i;e++)u=`00${u}`;if(r){const e=2*r-u.length;for(let t=0;t<e;t+=2)u=`00${u}`}return u}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}var n={};return(()=>{var e=n;Object.defineProperty(e,"__esModule",{value:!0}),e.b58ToC32=e.c32ToB58=e.versions=e.c32normalize=e.c32addressDecode=e.c32address=e.c32checkDecode=e.c32checkEncode=e.c32decode=e.c32encode=void 0;const t=r(558);Object.defineProperty(e,"c32encode",{enumerable:!0,get:function(){return t.c32encode}}),Object.defineProperty(e,"c32decode",{enumerable:!0,get:function(){return t.c32decode}}),Object.defineProperty(e,"c32normalize",{enumerable:!0,get:function(){return t.c32normalize}});const o=r(997);Object.defineProperty(e,"c32checkEncode",{enumerable:!0,get:function(){return o.c32checkEncode}}),Object.defineProperty(e,"c32checkDecode",{enumerable:!0,get:function(){return o.c32checkDecode}});const s=r(959);Object.defineProperty(e,"c32address",{enumerable:!0,get:function(){return s.c32address}}),Object.defineProperty(e,"c32addressDecode",{enumerable:!0,get:function(){return s.c32addressDecode}}),Object.defineProperty(e,"c32ToB58",{enumerable:!0,get:function(){return s.c32ToB58}}),Object.defineProperty(e,"b58ToC32",{enumerable:!0,get:function(){return s.b58ToC32}}),Object.defineProperty(e,"versions",{enumerable:!0,get:function(){return s.versions}})})(),n})()));
//# sourceMappingURL=c32check.js.map